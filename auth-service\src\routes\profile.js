/**
 * Profile Routes
 * Handles user profile and school-related endpoints
 */

const express = require('express');
const profileController = require('../controllers/profileController');
const { authenticateToken } = require('../middleware/auth');
const { validateBody, schemas } = require('../middleware/validation');

const router = express.Router();

/**
 * GET /auth/profile
 * Get current user's profile
 */
router.get('/profile',
  authenticateToken,
  profileController.getProfile
);

/**
 * PUT /auth/profile
 * Create or update current user's profile
 */
router.put('/profile',
  authenticateToken,
  validateBody(schemas.updateProfile),
  profileController.updateProfile
);

/**
 * DELETE /auth/profile
 * Delete current user's profile
 */
router.delete('/profile',
  authenticateToken,
  profileController.deleteProfile
);

/**
 * GET /auth/schools
 * Get list of schools with optional filtering
 * Query parameters:
 * - search: Search by school name
 * - city: Filter by city
 * - province: Filter by province
 * - page: Page number (default: 1)
 * - limit: Items per page (default: 20, max: 100)
 */
router.get('/schools',
  authenticateToken,
  profileController.getSchools
);

/**
 * POST /auth/schools
 * Create a new school (for admin use or user suggestions)
 */
router.post('/schools',
  authenticateToken,
  validateBody(schemas.createSchool),
  profileController.createSchool
);

module.exports = router;
