# New Tables Integration Guide

This document describes the integration of new tables `user_profiles` and `schools` into the ATMA backend services.

## Database Schema Changes

### 1. User Profiles Table (`auth.user_profiles`)

```sql
CREATE TABLE auth.user_profiles (
  user_id UUID PRIMARY KEY REFERENCES auth.users(id),
  full_name VA<PERSON><PERSON><PERSON>(100),
  school_origin VARCHAR(150),
  date_of_birth DATE,
  gender VARCHAR(10),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Purpose**: Store additional user profile information including demographics and educational background.

**Key Features**:
- One-to-one relationship with `auth.users`
- Optional fields for gradual profile completion
- Gender validation with predefined values
- Date of birth validation (cannot be in future)

### 2. Schools Table (`public.schools`)

```sql
CREATE TABLE public.schools (
  id SERIAL PRIMARY KEY,
  name VARCHAR(200) NOT NULL,
  address TEXT,
  city VARCHAR(100),
  province VARCHAR(100),
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Purpose**: Master data for educational institutions to standardize school references.

**Key Features**:
- Centralized school database
- Hierarchical location data (city, province)
- Extensible for future school-related features

## Service Integrations

### Auth Service (`auth-service/`)

#### New Models
- **UserProfile** (`src/models/UserProfile.js`)
  - Maps to `auth.user_profiles` table
  - Includes validation for gender and date_of_birth
  - Associated with User model (hasOne/belongsTo)

- **School** (`src/models/School.js`)
  - Maps to `public.schools` table
  - Read/write access for school management

#### New Controllers
- **ProfileController** (`src/controllers/profileController.js`)
  - `GET /auth/profile` - Get user profile
  - `PUT /auth/profile` - Create/update user profile
  - `DELETE /auth/profile` - Delete user profile
  - `GET /auth/schools` - List schools with filtering
  - `POST /auth/schools` - Create new school

#### New Routes
- **Profile Routes** (`src/routes/profile.js`)
  - All profile and school management endpoints
  - Integrated with authentication middleware
  - Input validation using Joi schemas

#### New Validation Schemas
- `updateProfile` - Validates profile update requests
- `createSchool` - Validates school creation requests

### Archive Service (`archive-service/`)

#### New Models (Read-Only)
- **UserProfile** (`src/models/UserProfile.js`)
  - Read-only access to `auth.user_profiles`
  - Associated with AnalysisResult for enriched queries

- **School** (`src/models/School.js`)
  - Read-only access to `public.schools`
  - Used for demographic analytics

#### New Services
- **DemographicService** (`src/services/demographicService.js`)
  - `getDemographicOverview()` - Overall demographic statistics
  - `getArchetypeDemographics(archetype)` - Archetype-specific demographics
  - `getSchoolAnalytics(schoolName)` - School-based analytics

#### New Controllers
- **DemographicController** (`src/controllers/demographicController.js`)
  - Handles demographic analytics endpoints
  - Internal service access only

#### New Routes
- **Demographics Routes** (`src/routes/demographics.js`)
  - `GET /archive/demographics/overview` - Overall demographics
  - `GET /archive/demographics/archetype/:archetype` - Archetype demographics
  - `GET /archive/demographics/schools` - School analytics

## API Endpoints

### Auth Service Endpoints

#### Profile Management
```
GET    /auth/profile           - Get current user's profile
PUT    /auth/profile           - Create/update user profile
DELETE /auth/profile           - Delete user profile
```

#### School Management
```
GET    /auth/schools           - List schools (with filtering)
POST   /auth/schools           - Create new school
```

**Query Parameters for GET /auth/schools**:
- `search` - Search by school name
- `city` - Filter by city
- `province` - Filter by province
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 20, max: 100)

### Archive Service Endpoints (Internal Only)

#### Demographic Analytics
```
GET /archive/demographics/overview                    - Overall demographic overview
GET /archive/demographics/archetype/:archetype       - Archetype-specific demographics
GET /archive/demographics/schools?school=name        - School-based analytics
```

## Data Models

### UserProfile Model
```javascript
{
  user_id: UUID,           // Primary key, references users.id
  full_name: String(100),  // Optional
  school_origin: String(150), // Optional
  date_of_birth: Date,     // Optional, must be in past
  gender: Enum,            // 'male', 'female', 'other', 'prefer_not_to_say'
  created_at: DateTime,
  updated_at: DateTime
}
```

### School Model
```javascript
{
  id: Integer,             // Auto-increment primary key
  name: String(200),       // Required
  address: Text,           // Optional
  city: String(100),       // Optional
  province: String(100),   // Optional
  created_at: DateTime
}
```

## Testing

### Test Scripts

#### Auth Service
```bash
node scripts/test-new-tables.js
```
Tests:
- Database connection
- Table existence verification
- Model operations (CRUD)
- Data validation
- Model associations

#### Archive Service
```bash
node scripts/test-new-models.js
```
Tests:
- Cross-schema access
- Model associations
- Enriched queries
- Demographic insights

## Usage Examples

### Creating User Profile
```javascript
// PUT /auth/profile
{
  "full_name": "John Doe",
  "school_origin": "University of Indonesia",
  "date_of_birth": "1995-06-15",
  "gender": "male"
}
```

### Getting Demographic Overview
```javascript
// GET /archive/demographics/overview (Internal only)
{
  "success": true,
  "data": {
    "gender_distribution": [...],
    "age_distribution": [...],
    "top_schools": [...],
    "archetype_by_gender": [...],
    "generated_at": "2024-01-15T10:30:00Z"
  }
}
```

## Benefits

### Enhanced Analytics
- Demographic insights for better understanding of user base
- School-based analytics for educational partnerships
- Archetype correlation with demographics

### Improved User Experience
- Personalized profile management
- School search and selection
- Better user segmentation

### Data Quality
- Standardized school references
- Validated demographic data
- Consistent data structure across services

## Migration Notes

1. **Database Tables**: Ensure both tables are created in the database before running the services
2. **Existing Users**: Profiles are optional - existing users can continue without profiles
3. **School Data**: Consider importing initial school data for better user experience
4. **Permissions**: Archive service has read-only access to auth schema tables

## Security Considerations

- Profile data is tied to authenticated users only
- Demographic endpoints are internal service only
- Input validation prevents data corruption
- No sensitive data exposure in demographic analytics

## Future Enhancements

- School verification system
- Profile completion incentives
- Advanced demographic filters
- Geographic analytics
- Educational outcome tracking
