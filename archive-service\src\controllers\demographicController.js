/**
 * Demographic Controller
 * Handles demographic analytics endpoints
 */

const demographicService = require('../services/demographicService');
const logger = require('../utils/logger');
const { formatPaginatedResponse } = require('../utils/responseFormatter');

/**
 * Get demographic overview
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getDemographicOverview = async (req, res, next) => {
  try {
    const overview = await demographicService.getDemographicOverview();
    
    logger.info('Demographic overview requested', {
      requestId: req.id,
      isInternalService: req.isInternalService
    });
    
    res.json({
      success: true,
      data: overview
    });
  } catch (error) {
    logger.error('Error getting demographic overview', {
      requestId: req.id,
      error: error.message,
      stack: error.stack
    });
    next(error);
  }
};

/**
 * Get archetype demographics
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getArchetypeDemographics = async (req, res, next) => {
  try {
    const { archetype } = req.params;
    
    if (!archetype) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'MISSING_ARCHETYPE',
          message: 'Archetype parameter is required'
        }
      });
    }
    
    const demographics = await demographicService.getArchetypeDemographics(archetype);
    
    logger.info('Archetype demographics requested', {
      requestId: req.id,
      archetype,
      isInternalService: req.isInternalService
    });
    
    res.json({
      success: true,
      data: demographics
    });
  } catch (error) {
    logger.error('Error getting archetype demographics', {
      requestId: req.id,
      archetype: req.params.archetype,
      error: error.message,
      stack: error.stack
    });
    next(error);
  }
};

/**
 * Get school analytics
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getSchoolAnalytics = async (req, res, next) => {
  try {
    const { school } = req.query;
    
    const analytics = await demographicService.getSchoolAnalytics(school);
    
    logger.info('School analytics requested', {
      requestId: req.id,
      schoolFilter: school,
      isInternalService: req.isInternalService
    });
    
    res.json({
      success: true,
      data: analytics
    });
  } catch (error) {
    logger.error('Error getting school analytics', {
      requestId: req.id,
      schoolFilter: req.query.school,
      error: error.message,
      stack: error.stack
    });
    next(error);
  }
};

module.exports = {
  getDemographicOverview,
  getArchetypeDemographics,
  getSchoolAnalytics
};
