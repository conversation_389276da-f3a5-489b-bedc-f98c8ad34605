/**
 * Demographic Service
 * Provides demographic insights and analytics based on user profiles
 */

const { AnalysisResult, UserProfile, School, sequelize } = require('../models');
const logger = require('../utils/logger');

/**
 * Get demographic overview of all analyses
 * @returns {Promise<Object>} Demographic overview
 */
const getDemographicOverview = async () => {
  try {
    // Gender distribution
    const genderStats = await sequelize.query(`
      SELECT 
        COALESCE(up.gender, 'unknown') as gender,
        COUNT(ar.id) as count,
        ROUND(COUNT(ar.id) * 100.0 / SUM(COUNT(ar.id)) OVER (), 2) as percentage
      FROM archive.analysis_results ar
      LEFT JOIN auth.user_profiles up ON ar.user_id = up.user_id
      WHERE ar.status = 'completed'
      GROUP BY up.gender
      ORDER BY count DESC
    `, {
      type: sequelize.QueryTypes.SELECT
    });

    // Age group distribution
    const ageStats = await sequelize.query(`
      SELECT 
        CASE 
          WHEN EXTRACT(YEAR FROM AGE(up.date_of_birth)) < 18 THEN 'Under 18'
          WHEN EXTRACT(YEAR FROM AGE(up.date_of_birth)) BETWEEN 18 AND 22 THEN '18-22'
          WHEN EXTRACT(YEAR FROM AGE(up.date_of_birth)) BETWEEN 23 AND 27 THEN '23-27'
          WHEN EXTRACT(YEAR FROM AGE(up.date_of_birth)) BETWEEN 28 AND 35 THEN '28-35'
          WHEN EXTRACT(YEAR FROM AGE(up.date_of_birth)) > 35 THEN 'Over 35'
          ELSE 'Unknown'
        END as age_group,
        COUNT(ar.id) as count,
        ROUND(COUNT(ar.id) * 100.0 / SUM(COUNT(ar.id)) OVER (), 2) as percentage
      FROM archive.analysis_results ar
      LEFT JOIN auth.user_profiles up ON ar.user_id = up.user_id
      WHERE ar.status = 'completed'
      GROUP BY age_group
      ORDER BY count DESC
    `, {
      type: sequelize.QueryTypes.SELECT
    });

    // Top schools by analysis count
    const schoolStats = await sequelize.query(`
      SELECT 
        COALESCE(up.school_origin, 'Unknown') as school_name,
        COUNT(ar.id) as analysis_count,
        COUNT(DISTINCT ar.user_id) as unique_users
      FROM archive.analysis_results ar
      LEFT JOIN auth.user_profiles up ON ar.user_id = up.user_id
      WHERE ar.status = 'completed'
      GROUP BY up.school_origin
      HAVING COUNT(ar.id) > 0
      ORDER BY analysis_count DESC
      LIMIT 10
    `, {
      type: sequelize.QueryTypes.SELECT
    });

    // Archetype distribution by gender
    const archetypeByGender = await sequelize.query(`
      SELECT 
        ar.persona_profile->>'archetype' as archetype,
        COALESCE(up.gender, 'unknown') as gender,
        COUNT(*) as count
      FROM archive.analysis_results ar
      LEFT JOIN auth.user_profiles up ON ar.user_id = up.user_id
      WHERE ar.status = 'completed'
        AND ar.persona_profile->>'archetype' IS NOT NULL
      GROUP BY ar.persona_profile->>'archetype', up.gender
      ORDER BY archetype, count DESC
    `, {
      type: sequelize.QueryTypes.SELECT
    });

    logger.info('Demographic overview generated', {
      genderGroups: genderStats.length,
      ageGroups: ageStats.length,
      topSchools: schoolStats.length
    });

    return {
      gender_distribution: genderStats,
      age_distribution: ageStats,
      top_schools: schoolStats,
      archetype_by_gender: archetypeByGender,
      generated_at: new Date().toISOString()
    };
  } catch (error) {
    logger.error('Error generating demographic overview', {
      error: error.message,
      stack: error.stack
    });
    throw error;
  }
};

/**
 * Get demographic insights for a specific archetype
 * @param {string} archetype - The archetype to analyze
 * @returns {Promise<Object>} Archetype demographic insights
 */
const getArchetypeDemographics = async (archetype) => {
  try {
    // Gender distribution for this archetype
    const genderDistribution = await sequelize.query(`
      SELECT 
        COALESCE(up.gender, 'unknown') as gender,
        COUNT(*) as count,
        ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (), 2) as percentage
      FROM archive.analysis_results ar
      LEFT JOIN auth.user_profiles up ON ar.user_id = up.user_id
      WHERE ar.status = 'completed'
        AND ar.persona_profile->>'archetype' = :archetype
      GROUP BY up.gender
      ORDER BY count DESC
    `, {
      replacements: { archetype },
      type: sequelize.QueryTypes.SELECT
    });

    // Age statistics for this archetype
    const ageStats = await sequelize.query(`
      SELECT 
        ROUND(AVG(EXTRACT(YEAR FROM AGE(up.date_of_birth))), 1) as average_age,
        MIN(EXTRACT(YEAR FROM AGE(up.date_of_birth))) as min_age,
        MAX(EXTRACT(YEAR FROM AGE(up.date_of_birth))) as max_age,
        COUNT(up.date_of_birth) as profiles_with_age
      FROM archive.analysis_results ar
      LEFT JOIN auth.user_profiles up ON ar.user_id = up.user_id
      WHERE ar.status = 'completed'
        AND ar.persona_profile->>'archetype' = :archetype
        AND up.date_of_birth IS NOT NULL
    `, {
      replacements: { archetype },
      type: sequelize.QueryTypes.SELECT
    });

    // Top schools for this archetype
    const topSchools = await sequelize.query(`
      SELECT 
        up.school_origin,
        COUNT(*) as count,
        ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (), 2) as percentage
      FROM archive.analysis_results ar
      LEFT JOIN auth.user_profiles up ON ar.user_id = up.user_id
      WHERE ar.status = 'completed'
        AND ar.persona_profile->>'archetype' = :archetype
        AND up.school_origin IS NOT NULL
      GROUP BY up.school_origin
      ORDER BY count DESC
      LIMIT 10
    `, {
      replacements: { archetype },
      type: sequelize.QueryTypes.SELECT
    });

    logger.info('Archetype demographics generated', {
      archetype,
      genderGroups: genderDistribution.length,
      topSchools: topSchools.length
    });

    return {
      archetype,
      gender_distribution: genderDistribution,
      age_statistics: ageStats[0] || null,
      top_schools: topSchools,
      generated_at: new Date().toISOString()
    };
  } catch (error) {
    logger.error('Error generating archetype demographics', {
      archetype,
      error: error.message,
      stack: error.stack
    });
    throw error;
  }
};

/**
 * Get school-based analytics
 * @param {string} schoolName - Optional school name filter
 * @returns {Promise<Object>} School analytics
 */
const getSchoolAnalytics = async (schoolName = null) => {
  try {
    let whereClause = '';
    let replacements = {};

    if (schoolName) {
      whereClause = 'AND up.school_origin ILIKE :schoolName';
      replacements.schoolName = `%${schoolName}%`;
    }

    // School performance overview
    const schoolStats = await sequelize.query(`
      SELECT 
        up.school_origin,
        COUNT(ar.id) as total_analyses,
        COUNT(DISTINCT ar.user_id) as unique_users,
        ROUND(AVG(EXTRACT(YEAR FROM AGE(up.date_of_birth))), 1) as avg_age,
        MODE() WITHIN GROUP (ORDER BY up.gender) as most_common_gender,
        MODE() WITHIN GROUP (ORDER BY ar.persona_profile->>'archetype') as most_common_archetype
      FROM archive.analysis_results ar
      LEFT JOIN auth.user_profiles up ON ar.user_id = up.user_id
      WHERE ar.status = 'completed'
        AND up.school_origin IS NOT NULL
        ${whereClause}
      GROUP BY up.school_origin
      HAVING COUNT(ar.id) >= 3
      ORDER BY total_analyses DESC
      LIMIT 20
    `, {
      replacements,
      type: sequelize.QueryTypes.SELECT
    });

    // Archetype distribution by schools
    const archetypeBySchool = await sequelize.query(`
      SELECT 
        up.school_origin,
        ar.persona_profile->>'archetype' as archetype,
        COUNT(*) as count
      FROM archive.analysis_results ar
      LEFT JOIN auth.user_profiles up ON ar.user_id = up.user_id
      WHERE ar.status = 'completed'
        AND up.school_origin IS NOT NULL
        AND ar.persona_profile->>'archetype' IS NOT NULL
        ${whereClause}
      GROUP BY up.school_origin, ar.persona_profile->>'archetype'
      ORDER BY up.school_origin, count DESC
    `, {
      replacements,
      type: sequelize.QueryTypes.SELECT
    });

    logger.info('School analytics generated', {
      schoolFilter: schoolName,
      schoolsAnalyzed: schoolStats.length
    });

    return {
      school_filter: schoolName,
      school_statistics: schoolStats,
      archetype_distribution: archetypeBySchool,
      generated_at: new Date().toISOString()
    };
  } catch (error) {
    logger.error('Error generating school analytics', {
      schoolName,
      error: error.message,
      stack: error.stack
    });
    throw error;
  }
};

module.exports = {
  getDemographicOverview,
  getArchetypeDemographics,
  getSchoolAnalytics
};
