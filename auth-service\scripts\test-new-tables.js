/**
 * Test script for new tables: user_profiles and schools
 * This script tests the new models and their associations
 */

const { User, UserProfile, School, sequelize } = require('../src/models');
const logger = require('../src/utils/logger');

/**
 * Test database connection
 */
async function testConnection() {
  try {
    await sequelize.authenticate();
    console.log('✅ Database connection established successfully');
    return true;
  } catch (error) {
    console.error('❌ Unable to connect to database:', error.message);
    return false;
  }
}

/**
 * Test if tables exist
 */
async function testTablesExist() {
  try {
    console.log('\n🔍 Checking if tables exist...');
    
    // Check user_profiles table
    const userProfilesResult = await sequelize.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'auth' 
        AND table_name = 'user_profiles'
      );
    `);
    
    const userProfilesExists = userProfilesResult[0][0].exists;
    console.log(`   auth.user_profiles: ${userProfilesExists ? '✅' : '❌'}`);
    
    // Check schools table
    const schoolsResult = await sequelize.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'schools'
      );
    `);
    
    const schoolsExists = schoolsResult[0][0].exists;
    console.log(`   public.schools: ${schoolsExists ? '✅' : '❌'}`);
    
    return userProfilesExists && schoolsExists;
  } catch (error) {
    console.error('❌ Error checking tables:', error.message);
    return false;
  }
}

/**
 * Test model operations
 */
async function testModelOperations() {
  try {
    console.log('\n🧪 Testing model operations...');
    
    // Test creating a test user first
    const testUser = await User.create({
      email: '<EMAIL>',
      password_hash: '$2b$12$test.hash.for.testing',
      token_balance: 10
    });
    console.log('✅ Test user created:', testUser.id);
    
    // Test creating user profile
    const testProfile = await UserProfile.create({
      user_id: testUser.id,
      full_name: 'Test User Profile',
      school_origin: 'Test University',
      date_of_birth: '1995-01-01',
      gender: 'other'
    });
    console.log('✅ User profile created for user:', testProfile.user_id);
    
    // Test creating school
    const testSchool = await School.create({
      name: 'Test University',
      address: 'Test Address 123',
      city: 'Test City',
      province: 'Test Province'
    });
    console.log('✅ School created:', testSchool.name);
    
    // Test associations
    const userWithProfile = await User.findByPk(testUser.id, {
      include: [{
        model: UserProfile,
        as: 'profile'
      }]
    });
    
    if (userWithProfile && userWithProfile.profile) {
      console.log('✅ User-Profile association working');
      console.log(`   User: ${userWithProfile.email}`);
      console.log(`   Profile: ${userWithProfile.profile.full_name}`);
    } else {
      console.log('❌ User-Profile association not working');
    }
    
    // Test finding schools
    const schools = await School.findAll({
      limit: 5
    });
    console.log(`✅ Found ${schools.length} schools`);
    
    // Cleanup test data
    await testProfile.destroy();
    await testUser.destroy();
    await testSchool.destroy();
    console.log('✅ Test data cleaned up');
    
    return true;
  } catch (error) {
    console.error('❌ Error in model operations:', error.message);
    console.error('Stack trace:', error.stack);
    return false;
  }
}

/**
 * Test data validation
 */
async function testValidation() {
  try {
    console.log('\n🔍 Testing data validation...');
    
    // Test invalid gender
    try {
      await UserProfile.build({
        user_id: '123e4567-e89b-12d3-a456-426614174000',
        gender: 'invalid_gender'
      }).validate();
      console.log('❌ Gender validation should have failed');
      return false;
    } catch (error) {
      console.log('✅ Gender validation working correctly');
    }
    
    // Test invalid date of birth (future date)
    try {
      await UserProfile.build({
        user_id: '123e4567-e89b-12d3-a456-426614174000',
        date_of_birth: '2030-01-01'
      }).validate();
      console.log('❌ Date of birth validation should have failed');
      return false;
    } catch (error) {
      console.log('✅ Date of birth validation working correctly');
    }
    
    // Test required school name
    try {
      await School.build({
        name: ''
      }).validate();
      console.log('❌ School name validation should have failed');
      return false;
    } catch (error) {
      console.log('✅ School name validation working correctly');
    }
    
    return true;
  } catch (error) {
    console.error('❌ Error in validation tests:', error.message);
    return false;
  }
}

/**
 * Main test function
 */
async function runTests() {
  try {
    console.log('🧪 Testing New Tables: user_profiles and schools\n');
    console.log('='.repeat(60));
    
    // Test connection
    const connectionOk = await testConnection();
    if (!connectionOk) {
      console.log('\n❌ Cannot proceed without database connection');
      return;
    }
    
    // Test tables exist
    const tablesExist = await testTablesExist();
    if (!tablesExist) {
      console.log('\n❌ Required tables do not exist. Please run the SQL commands to create them.');
      return;
    }
    
    // Test model operations
    const modelsOk = await testModelOperations();
    if (!modelsOk) {
      console.log('\n❌ Model operations failed');
      return;
    }
    
    // Test validation
    const validationOk = await testValidation();
    if (!validationOk) {
      console.log('\n❌ Validation tests failed');
      return;
    }
    
    console.log('\n' + '='.repeat(60));
    console.log('🎉 All tests passed! New tables are working correctly.');
    console.log('\nNew models available:');
    console.log('  - UserProfile (auth.user_profiles)');
    console.log('  - School (public.schools)');
    console.log('\nAssociations:');
    console.log('  - User.hasOne(UserProfile)');
    console.log('  - UserProfile.belongsTo(User)');
    
  } catch (error) {
    console.error('❌ Unexpected error:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    await sequelize.close();
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests();
}

module.exports = { runTests };
