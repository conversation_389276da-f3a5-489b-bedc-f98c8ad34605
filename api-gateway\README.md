# ATMA API Gateway

API Gateway untuk sistem ATMA (AI-Driven Talent Mapping Assessment) yang menghubungkan frontend dengan microservices backend.

## Overview

API Gateway berfungsi sebagai entry point tunggal untuk semua request client ke arsitektur microservices ATMA. Gateway ini menangani authentication, rate limiting, request routing, dan menyediakan interface API yang terpadu.

## Features

- **Request Routing**: Meneruskan request ke microservice yang sesuai
- **Authentication**: JWT-based authentication untuk user dan admin
- **Rate Limiting**: Pembatasan request per endpoint
- **Security**: Helmet.js security headers, CORS configuration
- **Logging**: Request/response logging dengan Winston
- **Health Checks**: Monitoring kesehatan service
- **Error Handling**: Centralized error handling

## Services

Gateway ini meneruskan request ke:
- **Auth Service**: Authentication dan profile management
- **Assessment Service**: Pemrosesan assessment psikologi
- **Archive Service**: Penyimpanan dan retrieval hasil assessment
- **Notification Service**: Email dan notifikasi

## Quick Start

```bash
# Install dependencies
npm install

# Start server
npm start          # Production
npm run dev        # Development
```

## Environment Variables

- `PORT`: Server port (default: 3000)
- `JWT_SECRET`: Secret key untuk JWT token
- `NODE_ENV`: Environment (development/production)
- Service URLs untuk setiap microservice

## API Documentation

Untuk dokumentasi lengkap API termasuk semua endpoint, request/response format, dan authentication requirements, lihat [API-DOCS.md](./API-DOCS.md).
