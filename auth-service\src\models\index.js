const sequelize = require('../config/database');
const User = require('./User');
const Admin = require('./Admin');
const UserProfile = require('./UserProfile');
const School = require('./School');

// Initialize models
const models = {
  User,
  Admin,
  UserProfile,
  School,
  sequelize
};

// Set up associations
Object.keys(models).forEach(modelName => {
  if (models[modelName].associate) {
    models[modelName].associate(models);
  }
});

module.exports = models;
