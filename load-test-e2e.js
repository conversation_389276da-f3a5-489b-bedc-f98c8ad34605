
const axios = require('axios');
const { performance } = require('perf_hooks');

// Configuration
const CONFIG = {
  API_BASE_URL: 'http://localhost:3000',
  CONCURRENT_USERS: parseInt(process.argv[2]) || 10,
  DELAY_BETWEEN_BATCHES: parseInt(process.argv[3]) || 500, // ms
  BATCH_SIZE: parseInt(process.argv[4]) || 5, // Process users in batches - configurable now
  MAX_WAIT_TIME: 300000, // 5 minutes max wait for processing
  POLL_INTERVAL: 5000, // Check results every 5 seconds
  USE_MOCK_AI: true // Set to true to use mock AI for faster testing
};

// Sample assessment data (all integers as required by validation)
const SAMPLE_ASSESSMENT = {
  riasec: {
    realistic: 75,
    investigative: 85,
    artistic: 60,
    social: 50,
    enterprising: 70,
    conventional: 55
  },
  ocean: {
    openness: 80,
    conscientiousness: 65,
    extraversion: 55,
    agreeableness: 45,
    neuroticism: 30
  },
  viaIs: {
    creativity: 85,
    curiosity: 78,
    judgment: 70,
    loveOfLearning: 82,
    perspective: 60,
    bravery: 55,
    perseverance: 68,
    honesty: 73,
    zest: 66,
    love: 80,
    kindness: 75,
    socialIntelligence: 65,
    teamwork: 60,
    fairness: 70,
    leadership: 67,
    forgiveness: 58,
    humility: 62,
    prudence: 69,
    selfRegulation: 61,
    appreciationOfBeauty: 50,
    gratitude: 72,
    hope: 77,
    humor: 65,
    spirituality: 55
  }
};

// Statistics tracking
const stats = {
  totalUsers: 0,
  successfulRegistrations: 0,
  successfulLogins: 0,
  successfulSubmissions: 0,
  successfulProcessing: 0,
  errors: {
    registration: 0,
    login: 0,
    submission: 0,
    processing: 0,
    timeout: 0
  },
  timings: {
    registration: [],
    login: [],
    submission: [],
    processing: [],
    endToEnd: []
  },
  startTime: null,
  endTime: null
};

/**
 * Generate random assessment data with variations
 */
function generateRandomAssessment() {
  const assessment = JSON.parse(JSON.stringify(SAMPLE_ASSESSMENT));

  // Add random variations (±20 points) and ensure integers
  Object.keys(assessment.riasec).forEach(key => {
    assessment.riasec[key] = Math.round(Math.max(0, Math.min(100,
      assessment.riasec[key] + (Math.random() - 0.5) * 40
    )));
  });

  Object.keys(assessment.ocean).forEach(key => {
    assessment.ocean[key] = Math.round(Math.max(0, Math.min(100,
      assessment.ocean[key] + (Math.random() - 0.5) * 40
    )));
  });

  Object.keys(assessment.viaIs).forEach(key => {
    assessment.viaIs[key] = Math.round(Math.max(0, Math.min(100,
      assessment.viaIs[key] + (Math.random() - 0.5) * 40
    )));
  });

  return assessment;
}

/**
 * Generate unique user credentials
 */
function generateUserCredentials(index) {
  const timestamp = Date.now();
  const randomId = Math.random().toString(36).substring(2, 8);
  return {
    email: `loadtest_${timestamp}_${index}_${randomId}@example.com`,
    password: `TestPass123_${randomId}`
  };
}

/**
 * Register a new user
 */
async function registerUser(credentials) {
  const startTime = performance.now();
  
  try {
    const response = await axios.post(`${CONFIG.API_BASE_URL}/auth/register`, credentials, {
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    const endTime = performance.now();
    stats.timings.registration.push(endTime - startTime);
    stats.successfulRegistrations++;
    
    return {
      success: true,
      data: response.data,
      timing: endTime - startTime
    };
  } catch (error) {
    const endTime = performance.now();
    stats.errors.registration++;
    
    return {
      success: false,
      error: error.response?.data || error.message,
      timing: endTime - startTime
    };
  }
}

/**
 * Login user
 */
async function loginUser(credentials) {
  const startTime = performance.now();
  
  try {
    const response = await axios.post(`${CONFIG.API_BASE_URL}/auth/login`, credentials, {
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    const endTime = performance.now();
    stats.timings.login.push(endTime - startTime);
    stats.successfulLogins++;
    
    return {
      success: true,
      data: response.data,
      token: response.data.data.token,
      timing: endTime - startTime
    };
  } catch (error) {
    const endTime = performance.now();
    stats.errors.login++;
    
    return {
      success: false,
      error: error.response?.data || error.message,
      timing: endTime - startTime
    };
  }
}

/**
 * Submit assessment
 */
async function submitAssessment(token, assessmentData) {
  const startTime = performance.now();
  
  try {
    const response = await axios.post(`${CONFIG.API_BASE_URL}/assessments/submit`, assessmentData, {
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });
    
    const endTime = performance.now();
    stats.timings.submission.push(endTime - startTime);
    stats.successfulSubmissions++;
    
    return {
      success: true,
      data: response.data,
      jobId: response.data.data.jobId,
      timing: endTime - startTime
    };
  } catch (error) {
    const endTime = performance.now();
    stats.errors.submission++;
    
    return {
      success: false,
      error: error.response?.data || error.message,
      timing: endTime - startTime
    };
  }
}

/**
 * Check assessment results
 */
async function checkResults(token) {
  try {
    const response = await axios.get(`${CONFIG.API_BASE_URL}/archive/results`, {
      timeout: 30000,
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    return {
      success: true,
      data: response.data,
      results: response.data.data.results || []
    };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data || error.message
    };
  }
}

/**
 * Wait for assessment processing to complete
 */
async function waitForProcessing(token, jobId, userIndex) {
  const startTime = performance.now();
  const maxWaitTime = CONFIG.MAX_WAIT_TIME;
  let elapsedTime = 0;

  while (elapsedTime < maxWaitTime) {
    await new Promise(resolve => setTimeout(resolve, CONFIG.POLL_INTERVAL));
    elapsedTime = performance.now() - startTime;

    const resultsCheck = await checkResults(token);

    if (resultsCheck.success && resultsCheck.results.length > 0) {
      // Found completed result
      const completedResult = resultsCheck.results.find(r => r.status === 'completed');
      if (completedResult) {
        const endTime = performance.now();
        stats.timings.processing.push(endTime - startTime);
        stats.successfulProcessing++;

        console.log(`✅ User ${userIndex}: Processing completed in ${Math.round(endTime - startTime)}ms`);
        return {
          success: true,
          result: completedResult,
          timing: endTime - startTime
        };
      }
    }

    // Log progress every 30 seconds
    if (Math.floor(elapsedTime / 30000) > Math.floor((elapsedTime - CONFIG.POLL_INTERVAL) / 30000)) {
      console.log(`⏳ User ${userIndex}: Still waiting... (${Math.round(elapsedTime/1000)}s elapsed)`);
    }
  }

  // Timeout
  stats.errors.timeout++;
  console.log(`⏰ User ${userIndex}: Timeout after ${Math.round(elapsedTime/1000)}s`);
  return {
    success: false,
    error: 'Processing timeout',
    timing: elapsedTime
  };
}

/**
 * Complete user journey for one user
 */
async function runUserJourney(userIndex) {
  const userStartTime = performance.now();
  const credentials = generateUserCredentials(userIndex);
  const assessmentData = generateRandomAssessment();

  console.log(`🚀 User ${userIndex}: Starting journey (${credentials.email})`);

  try {
    // Step 1: Register
    console.log(`📝 User ${userIndex}: Registering...`);
    const registerResult = await registerUser(credentials);
    if (!registerResult.success) {
      console.log(`❌ User ${userIndex}: Registration failed -`, registerResult.error);
      return { success: false, step: 'registration', error: registerResult.error };
    }
    console.log(`✅ User ${userIndex}: Registered in ${Math.round(registerResult.timing)}ms`);

    // Step 2: Login
    console.log(`🔐 User ${userIndex}: Logging in...`);
    const loginResult = await loginUser(credentials);
    if (!loginResult.success) {
      console.log(`❌ User ${userIndex}: Login failed -`, loginResult.error);
      return { success: false, step: 'login', error: loginResult.error };
    }
    console.log(`✅ User ${userIndex}: Logged in in ${Math.round(loginResult.timing)}ms`);

    // Step 3: Submit Assessment
    console.log(`📊 User ${userIndex}: Submitting assessment...`);
    const submitResult = await submitAssessment(loginResult.token, assessmentData);
    if (!submitResult.success) {
      console.log(`❌ User ${userIndex}: Assessment submission failed -`, submitResult.error);
      return { success: false, step: 'submission', error: submitResult.error };
    }
    console.log(`✅ User ${userIndex}: Assessment submitted in ${Math.round(submitResult.timing)}ms (Job: ${submitResult.jobId})`);

    // Step 4: Wait for Processing
    console.log(`⏳ User ${userIndex}: Waiting for processing...`);
    const processingResult = await waitForProcessing(loginResult.token, submitResult.jobId, userIndex);
    if (!processingResult.success) {
      console.log(`❌ User ${userIndex}: Processing failed -`, processingResult.error);
      return { success: false, step: 'processing', error: processingResult.error };
    }

    // Success!
    const userEndTime = performance.now();
    const totalTime = userEndTime - userStartTime;
    stats.timings.endToEnd.push(totalTime);

    console.log(`🎉 User ${userIndex}: Complete journey finished in ${Math.round(totalTime)}ms`);
    console.log(`📋 User ${userIndex}: Result saved with ID: ${processingResult.result.id}`);

    return {
      success: true,
      timing: totalTime,
      result: processingResult.result,
      credentials: credentials
    };

  } catch (error) {
    console.log(`💥 User ${userIndex}: Unexpected error -`, error.message);
    return { success: false, step: 'unexpected', error: error.message };
  }
}

/**
 * Calculate statistics
 */
function calculateStats(timings) {
  if (timings.length === 0) return { avg: 0, min: 0, max: 0, p95: 0 };

  const sorted = timings.slice().sort((a, b) => a - b);
  const avg = timings.reduce((a, b) => a + b, 0) / timings.length;
  const min = sorted[0];
  const max = sorted[sorted.length - 1];
  const p95Index = Math.floor(sorted.length * 0.95);
  const p95 = sorted[p95Index] || max;

  return { avg, min, max, p95 };
}

/**
 * Print final report
 */
function printReport() {
  const totalTime = stats.endTime - stats.startTime;
  const successRate = (stats.successfulProcessing / stats.totalUsers) * 100;

  console.log('\n' + '='.repeat(80));
  console.log('🏁 LOAD TEST RESULTS');
  console.log('='.repeat(80));

  console.log(`\n📊 OVERVIEW:`);
  console.log(`   Total Users: ${stats.totalUsers}`);
  console.log(`   Overall Success Rate: ${successRate.toFixed(1)}% (${stats.successfulProcessing}/${stats.totalUsers})`);
  console.log(`   Total Time: ${Math.round(totalTime/1000)}s`);
  console.log(`   Throughput: ${(stats.successfulProcessing / (totalTime/1000)).toFixed(2)} users/second`);

  console.log(`\n📈 STEP-BY-STEP SUCCESS BREAKDOWN:`);
  console.log(`   👤 Registration: ${stats.successfulRegistrations}/${stats.totalUsers} (${(stats.successfulRegistrations/stats.totalUsers*100).toFixed(1)}%)`);
  console.log(`   🔐 Login: ${stats.successfulLogins}/${stats.totalUsers} (${(stats.successfulLogins/stats.totalUsers*100).toFixed(1)}%)`);
  console.log(`   📊 Assessment Submission: ${stats.successfulSubmissions}/${stats.totalUsers} (${(stats.successfulSubmissions/stats.totalUsers*100).toFixed(1)}%)`);
  console.log(`   🤖 AI Processing: ${stats.successfulProcessing}/${stats.totalUsers} (${(stats.successfulProcessing/stats.totalUsers*100).toFixed(1)}%)`);

  console.log(`\n❌ ERROR BREAKDOWN:`);
  console.log(`   Registration Errors: ${stats.errors.registration}`);
  console.log(`   Login Errors: ${stats.errors.login}`);
  console.log(`   Submission Errors: ${stats.errors.submission}`);
  console.log(`   Processing Errors: ${stats.errors.processing}`);
  console.log(`   Timeout Errors: ${stats.errors.timeout}`);

  console.log(`\n⏱️  TIMING STATISTICS (ms):`);

  const regStats = calculateStats(stats.timings.registration);
  console.log(`   Registration - Avg: ${Math.round(regStats.avg)}, Min: ${Math.round(regStats.min)}, Max: ${Math.round(regStats.max)}, P95: ${Math.round(regStats.p95)}`);

  const loginStats = calculateStats(stats.timings.login);
  console.log(`   Login - Avg: ${Math.round(loginStats.avg)}, Min: ${Math.round(loginStats.min)}, Max: ${Math.round(loginStats.max)}, P95: ${Math.round(loginStats.p95)}`);

  const submitStats = calculateStats(stats.timings.submission);
  console.log(`   Submission - Avg: ${Math.round(submitStats.avg)}, Min: ${Math.round(submitStats.min)}, Max: ${Math.round(submitStats.max)}, P95: ${Math.round(submitStats.p95)}`);

  const procStats = calculateStats(stats.timings.processing);
  console.log(`   Processing - Avg: ${Math.round(procStats.avg)}, Min: ${Math.round(procStats.min)}, Max: ${Math.round(procStats.max)}, P95: ${Math.round(procStats.p95)}`);

  const e2eStats = calculateStats(stats.timings.endToEnd);
  console.log(`   End-to-End - Avg: ${Math.round(e2eStats.avg)}, Min: ${Math.round(e2eStats.min)}, Max: ${Math.round(e2eStats.max)}, P95: ${Math.round(e2eStats.p95)}`);

  // Calculate conversion rates
  const registrationRate = (stats.successfulRegistrations / stats.totalUsers) * 100;
  const loginRate = stats.successfulRegistrations > 0 ? (stats.successfulLogins / stats.successfulRegistrations) * 100 : 0;
  const submissionRate = stats.successfulLogins > 0 ? (stats.successfulSubmissions / stats.successfulLogins) * 100 : 0;
  const processingRate = stats.successfulSubmissions > 0 ? (stats.successfulProcessing / stats.successfulSubmissions) * 100 : 0;

  console.log(`\n🔄 CONVERSION FUNNEL ANALYSIS:`);
  console.log(`   ${stats.totalUsers} Users Started`);
  console.log(`   ↓ ${registrationRate.toFixed(1)}% conversion`);
  console.log(`   ${stats.successfulRegistrations} Registered Successfully`);
  console.log(`   ↓ ${loginRate.toFixed(1)}% conversion`);
  console.log(`   ${stats.successfulLogins} Logged In Successfully`);
  console.log(`   ↓ ${submissionRate.toFixed(1)}% conversion`);
  console.log(`   ${stats.successfulSubmissions} Submitted Assessment Successfully`);
  console.log(`   ↓ ${processingRate.toFixed(1)}% conversion`);
  console.log(`   ${stats.successfulProcessing} Completed Full Journey`);

  console.log(`\n📋 SUMMARY:`);
  console.log(`   • ${stats.successfulRegistrations} users successfully registered`);
  console.log(`   • ${stats.successfulLogins} users successfully logged in`);
  console.log(`   • ${stats.successfulSubmissions} users successfully submitted assessments`);
  console.log(`   • ${stats.successfulProcessing} users successfully completed AI processing`);
  console.log(`   • ${stats.totalUsers - stats.successfulProcessing} users failed to complete the full journey`);

  console.log('\n' + '='.repeat(80));

  // Performance assessment
  if (successRate >= 95) {
    console.log('🟢 EXCELLENT: System handled the load very well!');
  } else if (successRate >= 80) {
    console.log('🟡 GOOD: System handled most requests, some optimization needed.');
  } else if (successRate >= 60) {
    console.log('🟠 FAIR: System struggled with the load, significant optimization needed.');
  } else {
    console.log('🔴 POOR: System failed to handle the load, major issues need addressing.');
  }

  // Bottleneck analysis
  console.log(`\n🔍 BOTTLENECK ANALYSIS:`);
  const totalErrors = stats.errors.registration + stats.errors.login + stats.errors.submission + stats.errors.processing + stats.errors.timeout;

  if (totalErrors === 0) {
    console.log('   ✅ No bottlenecks detected - all operations completed successfully');
  } else {
    if (stats.errors.registration > 0) {
      console.log(`   🚨 Registration bottleneck: ${stats.errors.registration} failures (${(stats.errors.registration/stats.totalUsers*100).toFixed(1)}%)`);
    }
    if (stats.errors.login > 0) {
      console.log(`   🚨 Login bottleneck: ${stats.errors.login} failures (${(stats.errors.login/stats.totalUsers*100).toFixed(1)}%)`);
    }
    if (stats.errors.submission > 0) {
      console.log(`   🚨 Assessment submission bottleneck: ${stats.errors.submission} failures (${(stats.errors.submission/stats.totalUsers*100).toFixed(1)}%)`);
    }
    if (stats.errors.processing > 0) {
      console.log(`   🚨 AI processing bottleneck: ${stats.errors.processing} failures (${(stats.errors.processing/stats.totalUsers*100).toFixed(1)}%)`);
    }
    if (stats.errors.timeout > 0) {
      console.log(`   🚨 Timeout bottleneck: ${stats.errors.timeout} timeouts (${(stats.errors.timeout/stats.totalUsers*100).toFixed(1)}%)`);
    }
  }

  console.log('\n💡 RECOMMENDATIONS:');
  if (stats.errors.timeout > 0) {
    console.log('   - Consider increasing worker capacity or optimizing AI processing time');
  }
  if (stats.errors.submission > 0) {
    console.log('   - Check assessment service capacity and rate limiting');
  }
  if (stats.errors.registration > 0) {
    console.log('   - Check database connection pool and user registration service');
  }
  if (stats.errors.login > 0) {
    console.log('   - Check authentication service and database performance');
  }
  if (e2eStats.avg > 60000) {
    console.log('   - End-to-end time is high, consider optimizing the processing pipeline');
  }
  if (successRate < 90) {
    console.log('   - Success rate is below 90%, investigate error patterns');
  }
  if (registrationRate < 95) {
    console.log('   - Registration success rate is low, check database capacity');
  }
  if (loginRate < 95 && registrationRate >= 95) {
    console.log('   - Login success rate is low despite good registration, check auth service');
  }
  if (processingRate < 95 && submissionRate >= 95) {
    console.log('   - AI processing success rate is low, check worker capacity and AI service');
  }
}

/**
 * Check system health before starting
 */
async function checkSystemHealth() {
  console.log('🔍 Checking system health...');

  try {
    // Check API Gateway
    const healthResponse = await axios.get(`${CONFIG.API_BASE_URL}/health/live`, {
      timeout: 10000
    });

    if (healthResponse.status === 200) {
      console.log('✅ API Gateway is healthy');
    } else {
      throw new Error(`API Gateway health check failed: ${healthResponse.status}`);
    }

    // Check if we can reach the main endpoint
    const rootResponse = await axios.get(`${CONFIG.API_BASE_URL}/`, {
      timeout: 10000
    });

    if (rootResponse.status === 200) {
      console.log('✅ API Gateway root endpoint is accessible');
    } else {
      throw new Error(`API Gateway root endpoint failed: ${rootResponse.status}`);
    }

    return true;
  } catch (error) {
    console.log('❌ System health check failed:', error.message);
    console.log('💡 Make sure all services are running using: start-all-simple.bat');
    return false;
  }
}

/**
 * Main execution function
 */
async function main() {
  console.log('🎯 ATMA Backend End-to-End Load Test');
  console.log('=====================================');
  console.log(`📊 Configuration:`);
  console.log(`   Concurrent Users: ${CONFIG.CONCURRENT_USERS}`);
  console.log(`   Batch Size: ${CONFIG.BATCH_SIZE}`);
  console.log(`   Delay Between Batches: ${CONFIG.DELAY_BETWEEN_BATCHES}ms`);
  console.log(`   Max Wait Time: ${CONFIG.MAX_WAIT_TIME/1000}s`);
  console.log(`   API Base URL: ${CONFIG.API_BASE_URL}`);
  console.log(`   Using Mock AI: ${CONFIG.USE_MOCK_AI}`);

  // Calculate and show batch strategy
  const numBatches = Math.ceil(CONFIG.CONCURRENT_USERS / CONFIG.BATCH_SIZE);
  const actualBatchSize = CONFIG.BATCH_SIZE >= CONFIG.CONCURRENT_USERS ? CONFIG.CONCURRENT_USERS : CONFIG.BATCH_SIZE;

  if (CONFIG.BATCH_SIZE >= CONFIG.CONCURRENT_USERS) {
    console.log(`   Strategy: ALL ${CONFIG.CONCURRENT_USERS} users at once (maximum load)`);
  } else {
    console.log(`   Strategy: ${numBatches} batches of ${actualBatchSize} users`);
  }
  console.log('');

  // Check system health first
  const isHealthy = await checkSystemHealth();
  if (!isHealthy) {
    console.log('\n❌ System health check failed. Exiting...');
    process.exit(1);
  }

  console.log('\n🚀 Starting load test...\n');

  stats.startTime = performance.now();
  stats.totalUsers = CONFIG.CONCURRENT_USERS;

  // Process users in batches to avoid overwhelming the system
  const batches = [];
  for (let i = 0; i < CONFIG.CONCURRENT_USERS; i += CONFIG.BATCH_SIZE) {
    const batchEnd = Math.min(i + CONFIG.BATCH_SIZE, CONFIG.CONCURRENT_USERS);
    batches.push({ start: i, end: batchEnd });
  }

  if (batches.length === 1) {
    console.log(`📦 Processing ALL ${CONFIG.CONCURRENT_USERS} users simultaneously (single batch)`);
  } else {
    console.log(`📦 Processing ${CONFIG.CONCURRENT_USERS} users in ${batches.length} batches of up to ${CONFIG.BATCH_SIZE}`);
  }
  console.log('');

  // Execute batches
  for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
    const batch = batches[batchIndex];
    console.log(`🔄 Starting batch ${batchIndex + 1}/${batches.length} (Users ${batch.start + 1}-${batch.end})`);

    // Create promises for this batch
    const batchPromises = [];
    for (let userIndex = batch.start; userIndex < batch.end; userIndex++) {
      batchPromises.push(runUserJourney(userIndex + 1));
    }

    // Wait for batch to complete
    await Promise.allSettled(batchPromises);

    // Delay between batches (except for the last one)
    if (batchIndex < batches.length - 1) {
      console.log(`⏸️  Waiting ${CONFIG.DELAY_BETWEEN_BATCHES}ms before next batch...\n`);
      await new Promise(resolve => setTimeout(resolve, CONFIG.DELAY_BETWEEN_BATCHES));
    }
  }

  stats.endTime = performance.now();

  console.log('\n⏳ All users submitted. Waiting for any remaining processing...');

  // Wait a bit more for any remaining processing
  await new Promise(resolve => setTimeout(resolve, 10000));

  // Print final report
  printReport();
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n\n⚠️  Received SIGINT. Generating report with current data...');
  stats.endTime = performance.now();
  printReport();
  process.exit(0);
});

// Handle uncaught errors
process.on('uncaughtException', (error) => {
  console.error('\n💥 Uncaught Exception:', error);
  stats.endTime = performance.now();
  printReport();
  process.exit(1);
});

// Run the load test
if (require.main === module) {
  main().catch(error => {
    console.error('\n💥 Load test failed:', error);
    process.exit(1);
  });
}

module.exports = {
  runUserJourney,
  checkSystemHealth,
  CONFIG
};
