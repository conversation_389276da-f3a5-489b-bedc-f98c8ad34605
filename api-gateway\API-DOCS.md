# ATMA API Gateway - API Documentation

## Status Codes

- **200**: Success
- **201**: Created
- **401**: Unauthorized (token required/invalid)
- **403**: Forbidden (insufficient permissions)
- **404**: Not Found
- **429**: Too Many Requests (rate limit exceeded)
- **503**: Service Unavailable

## Authentication

Semua endpoint yang memerlukan authentication menggunakan JWT Bearer token:
```
Authorization: Bearer <jwt_token>
```

## Base URL

```
http://localhost:3000
```

---

## Root Endpoint

### GET /
Status API Gateway

**Response:**
```json
{
  "success": true,
  "message": "ATMA API Gateway is running",
  "version": "1.0.0",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

---

## Authentication Endpoints

### POST /auth/register
Registrasi user baru

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "User registered successfully",
  "data": {
    "user": {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "email": "<EMAIL>",
      "token_balance": 5,
      "created_at": "2024-01-01T00:00:00.000Z"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

### POST /auth/login
Login user

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "email": "<EMAIL>",
      "token_balance": 5,
      "created_at": "2024-01-01T00:00:00.000Z",
      "updated_at": "2024-01-01T00:00:00.000Z"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

### GET /auth/profile
Get user profile (requires authentication)

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "email": "<EMAIL>",
      "token_balance": 5,
      "created_at": "2024-01-01T00:00:00.000Z",
      "updated_at": "2024-01-01T00:00:00.000Z"
    }
  }
}
```

### PUT /auth/profile
Update user profile (requires authentication)

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Profile updated successfully",
  "data": {
    "user": {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "email": "<EMAIL>",
      "token_balance": 5,
      "updated_at": "2024-01-01T00:01:00.000Z"
    }
  }
}
```

### POST /auth/change-password
Change password (requires authentication)

**Request Body:**
```json
{
  "currentPassword": "old_password",
  "newPassword": "new_password123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Password changed successfully"
}
```

### POST /auth/logout
Logout user (requires authentication)

**Response:**
```json
{
  "success": true,
  "message": "Logout successful"
}
```

### GET /auth/token-balance
Get user token balance (requires authentication)

**Response:**
```json
{
  "success": true,
  "data": {
    "user_id": "550e8400-e29b-41d4-a716-446655440000",
    "token_balance": 5
  }
}
```

---

## Assessment Endpoints

*Semua endpoint assessment memerlukan authentication*

### POST /assessments/submit
Submit assessment data

**Request Body:**
```json
{
  "riasec": {
    "realistic": 75,
    "investigative": 80,
    "artistic": 60,
    "social": 85,
    "enterprising": 70,
    "conventional": 65
  },
  "ocean": {
    "openness": 75,
    "conscientiousness": 80,
    "extraversion": 70,
    "agreeableness": 85,
    "neuroticism": 40
  },
  "viaIs": {
    "creativity": 80,
    "curiosity": 75,
    "judgment": 70,
    "loveOfLearning": 85,
    "perspective": 75,
    "bravery": 70,
    "perseverance": 80,
    "honesty": 85,
    "zest": 75,
    "love": 80,
    "kindness": 85,
    "socialIntelligence": 75,
    "teamwork": 80,
    "fairness": 85,
    "leadership": 70,
    "forgiveness": 75,
    "humility": 70,
    "prudence": 75,
    "selfRegulation": 80,
    "appreciationOfBeauty": 70,
    "gratitude": 85,
    "hope": 80,
    "humor": 75,
    "spirituality": 60
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Assessment submitted successfully and queued for analysis",
  "data": {
    "jobId": "550e8400-e29b-41d4-a716-446655440000",
    "status": "queued",
    "estimatedProcessingTime": "2-5 minutes",
    "queuePosition": 3,
    "tokenCost": 1,
    "remainingTokens": 99
  }
}
```

### GET /assessments/status/:jobId
Check assessment processing status

**Response:**
```json
{
  "success": true,
  "message": "Job status retrieved successfully",
  "data": {
    "jobId": "550e8400-e29b-41d4-a716-446655440000",
    "status": "queued|processing|completed|failed",
    "progress": 75,
    "estimatedTimeRemaining": "2-5 minutes",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:01:30.000Z"
  }
}
```

---

## Archive Endpoints

*Semua endpoint archive memerlukan authentication*

### GET /archive/results
Get user's assessment results

**Query Parameters:**
- `page`: Page number (optional, default: 1)
- `limit`: Results per page (optional, default: 10)
- `status`: Filter by status (optional)
- `sort`: Sort field (optional)
- `order`: Sort order asc/desc (optional)

**Response:**
```json
{
  "success": true,
  "data": {
    "results": [
      {
        "id": "550e8400-e29b-41d4-a716-446655440000",
        "user_id": "550e8400-e29b-41d4-a716-446655440001",
        "persona_profile": [
          {
            "archetype": "The Innovator",
            "personality_summary": "Creative and analytical thinker..."
          }
        ],
        "status": "completed",
        "created_at": "2024-01-01T00:00:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "totalPages": 3,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

### GET /archive/results/:id
Get specific assessment result

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "user_id": "550e8400-e29b-41d4-a716-446655440001",
    "assessment_data": {
      "riasec": { "realistic": 75, "investigative": 80 },
      "ocean": { "openness": 75, "conscientiousness": 80 },
      "viaIs": { "creativity": 80, "curiosity": 75 }
    },
    "persona_profile": [
      {
        "archetype": "The Innovator",
        "personality_summary": "Creative and analytical thinker...",
        "strengths": ["Creative thinking", "Problem solving"],
        "career_recommendations": ["Software Developer", "Research Scientist"]
      }
    ],
    "status": "completed",
    "created_at": "2024-01-01T00:00:00.000Z"
  }
}
```

### PUT /archive/results/:id
Update assessment result (user can update their own results)

**Request Body:**
```json
{
  "status": "completed",
  "persona_profile": [
    {
      "archetype": "The Updated Innovator",
      "personality_summary": "Updated summary..."
    }
  ]
}
```

### DELETE /archive/results/:id
Delete assessment result

**Response:**
```json
{
  "success": true,
  "message": "Analysis result deleted successfully"
}
```

### GET /archive/stats
Get user statistics

**Response:**
```json
{
  "success": true,
  "data": {
    "total_analyses": 15,
    "completed": 12,
    "processing": 2,
    "failed": 1,
    "latest_analysis": "2024-01-01T00:00:00.000Z",
    "most_common_archetype": "The Innovator"
  }
}
```

### GET /archive/stats/overview
Get user statistics overview

**Response:**
```json
{
  "success": true,
  "data": {
    "total_assessments": 15,
    "completed_assessments": 12,
    "latest_assessment_date": "2024-01-01T00:00:00.000Z",
    "primary_archetype": "The Innovator"
  }
}
```

---

## Admin Endpoints

*Semua endpoint admin memerlukan admin authentication*

### POST /admin/login
Admin login

**Request Body:**
```json
{
  "username": "admin",
  "password": "admin_password"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "admin": {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "username": "admin",
      "email": "<EMAIL>",
      "full_name": "Admin User",
      "role": "admin"
    },
    "token": "jwt_token_here"
  }
}
```

### GET /admin/profile
Get admin profile (requires admin auth)

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "username": "admin",
    "email": "<EMAIL>",
    "full_name": "Admin User",
    "role": "admin",
    "created_at": "2024-01-01T00:00:00.000Z"
  }
}
```

### PUT /admin/profile
Update admin profile (requires admin auth)

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "full_name": "New Admin Name"
}
```

### POST /admin/change-password
Change admin password (requires admin auth)

**Request Body:**
```json
{
  "currentPassword": "old_password",
  "newPassword": "NewPassword123!"
}
```

### POST /admin/logout
Admin logout (requires admin auth)

**Response:**
```json
{
  "success": true,
  "message": "Logout successful"
}
```

### POST /admin/register
Register new admin (requires superadmin role)

**Request Body:**
```json
{
  "username": "newadmin",
  "email": "<EMAIL>",
  "password": "AdminPassword123!",
  "full_name": "New Admin",
  "role": "admin"
}
```

### GET /admin/users
Get all users (requires admin/superadmin role)

**Query Parameters:**
- `page`: Page number (optional, default: 1)
- `limit`: Users per page (optional, default: 10)
- `search`: Search by email (optional)
- `sortBy`: Sort field (email, token_balance, created_at, updated_at)
- `sortOrder`: Sort order (ASC, DESC)

**Response:**
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": "550e8400-e29b-41d4-a716-446655440000",
        "email": "<EMAIL>",
        "token_balance": 100,
        "created_at": "2024-01-01T00:00:00.000Z",
        "updated_at": "2024-01-01T00:00:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "totalPages": 3,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

### GET /admin/users/:userId
Get specific user (requires admin/superadmin role)

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "email": "<EMAIL>",
    "token_balance": 100,
    "created_at": "2024-01-01T00:00:00.000Z",
    "updated_at": "2024-01-01T00:00:00.000Z"
  }
}
```

### PUT /admin/users/:userId/token-balance
Update user token balance (requires admin/superadmin role)

**Request Body:**
```json
{
  "token_balance": 100,
  "action": "set"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Token balance updated successfully",
  "data": {
    "user": {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "email": "<EMAIL>",
      "token_balance": 100,
      "updated_at": "2024-01-01T00:00:00.000Z"
    },
    "change": {
      "action": "set",
      "amount": 100,
      "previousBalance": 50,
      "newBalance": 100
    }
  }
}
```

### DELETE /admin/users/:userId
Delete user (requires superadmin role)

**Response:**
```json
{
  "success": true,
  "message": "User deleted successfully"
}
```

---

## Health Check Endpoints

### GET /health
Basic health check

**Response:**
```json
{
  "status": "healthy|degraded",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "responseTime": "50ms",
  "services": {
    "auth-service": { "status": "healthy" },
    "assessment-service": { "status": "healthy" },
    "archive-service": { "status": "healthy" },
    "notification-service": { "status": "healthy" }
  }
}
```

### GET /health/detailed
Detailed health check with system info

### GET /health/live
Liveness probe

### GET /health/ready
Readiness probe

---

## Rate Limiting

- **General**: 1000 requests per 10 minutes (per IP)
- **Auth endpoints**: 1000 requests per 10 minutes (per IP, only failed requests counted)
- **Assessment endpoints**: 1000 requests per 10 minutes (per user)
