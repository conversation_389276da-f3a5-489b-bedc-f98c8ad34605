/**
 * Profile Controller
 * Handles user profile operations
 */

const { User, UserProfile, School } = require('../models');
const logger = require('../utils/logger');
const { formatResponse, formatErrorResponse } = require('../utils/responseFormatter');

/**
 * Get user profile
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getProfile = async (req, res, next) => {
  try {
    const userId = req.user.id;
    
    const user = await User.findByPk(userId, {
      include: [{
        model: UserProfile,
        as: 'profile',
        required: false
      }],
      attributes: { exclude: ['password_hash'] }
    });
    
    if (!user) {
      return res.status(404).json(formatErrorResponse(
        'USER_NOT_FOUND',
        'User not found'
      ));
    }
    
    logger.info('User profile retrieved', {
      userId,
      hasProfile: !!user.profile
    });
    
    res.json(formatResponse({
      user: {
        id: user.id,
        email: user.email,
        token_balance: user.token_balance,
        created_at: user.created_at,
        profile: user.profile || null
      }
    }));
  } catch (error) {
    logger.error('Error retrieving user profile', {
      userId: req.user?.id,
      error: error.message,
      stack: error.stack
    });
    next(error);
  }
};

/**
 * Create or update user profile
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const updateProfile = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const {
      full_name,
      school_origin,
      date_of_birth,
      gender
    } = req.body;
    
    // Check if user exists
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json(formatErrorResponse(
        'USER_NOT_FOUND',
        'User not found'
      ));
    }
    
    // Check if profile already exists
    let profile = await UserProfile.findByPk(userId);
    
    const profileData = {
      user_id: userId,
      full_name,
      school_origin,
      date_of_birth,
      gender
    };
    
    if (profile) {
      // Update existing profile
      await profile.update(profileData);
      logger.info('User profile updated', {
        userId,
        changes: Object.keys(req.body)
      });
    } else {
      // Create new profile
      profile = await UserProfile.create(profileData);
      logger.info('User profile created', {
        userId
      });
    }
    
    res.json(formatResponse({
      profile,
      message: profile ? 'Profile updated successfully' : 'Profile created successfully'
    }));
  } catch (error) {
    logger.error('Error updating user profile', {
      userId: req.user?.id,
      error: error.message,
      stack: error.stack
    });
    next(error);
  }
};

/**
 * Delete user profile
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const deleteProfile = async (req, res, next) => {
  try {
    const userId = req.user.id;
    
    const profile = await UserProfile.findByPk(userId);
    if (!profile) {
      return res.status(404).json(formatErrorResponse(
        'PROFILE_NOT_FOUND',
        'Profile not found'
      ));
    }
    
    await profile.destroy();
    
    logger.info('User profile deleted', {
      userId
    });
    
    res.json(formatResponse({
      message: 'Profile deleted successfully'
    }));
  } catch (error) {
    logger.error('Error deleting user profile', {
      userId: req.user?.id,
      error: error.message,
      stack: error.stack
    });
    next(error);
  }
};

/**
 * Get schools list
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getSchools = async (req, res, next) => {
  try {
    const { search, city, province, page = 1, limit = 20 } = req.query;
    
    const offset = (page - 1) * limit;
    const where = {};
    
    if (search) {
      where.name = {
        [require('sequelize').Op.iLike]: `%${search}%`
      };
    }
    
    if (city) {
      where.city = {
        [require('sequelize').Op.iLike]: `%${city}%`
      };
    }
    
    if (province) {
      where.province = {
        [require('sequelize').Op.iLike]: `%${province}%`
      };
    }
    
    const { count, rows: schools } = await School.findAndCountAll({
      where,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['name', 'ASC']]
    });
    
    logger.info('Schools list retrieved', {
      count,
      page,
      limit,
      filters: { search, city, province }
    });
    
    res.json(formatResponse({
      schools,
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(count / limit)
      }
    }));
  } catch (error) {
    logger.error('Error retrieving schools', {
      error: error.message,
      stack: error.stack
    });
    next(error);
  }
};

/**
 * Create new school
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const createSchool = async (req, res, next) => {
  try {
    const { name, address, city, province } = req.body;
    
    const school = await School.create({
      name,
      address,
      city,
      province
    });
    
    logger.info('School created', {
      schoolId: school.id,
      name: school.name,
      city: school.city
    });
    
    res.status(201).json(formatResponse({
      school,
      message: 'School created successfully'
    }));
  } catch (error) {
    logger.error('Error creating school', {
      error: error.message,
      stack: error.stack
    });
    next(error);
  }
};

module.exports = {
  getProfile,
  updateProfile,
  deleteProfile,
  getSchools,
  createSchool
};
