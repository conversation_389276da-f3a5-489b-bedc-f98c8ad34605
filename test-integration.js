/**
 * Integration Test Script
 * Tests the complete integration of new tables across both services
 */

const path = require('path');

// Test both services
async function runIntegrationTests() {
  console.log('🧪 Running Complete Integration Tests\n');
  console.log('='.repeat(80));
  
  try {
    // Test Auth Service
    console.log('\n📋 Testing Auth Service...');
    console.log('-'.repeat(40));
    
    const authTestPath = path.join(__dirname, 'auth-service', 'scripts', 'test-new-tables.js');
    const { runTests: runAuthTests } = require(authTestPath);
    
    await runAuthTests();
    
    // Test Archive Service
    console.log('\n📊 Testing Archive Service...');
    console.log('-'.repeat(40));
    
    const archiveTestPath = path.join(__dirname, 'archive-service', 'scripts', 'test-new-models.js');
    const { runTests: runArchiveTests } = require(archiveTestPath);
    
    await runArchiveTests();
    
    console.log('\n' + '='.repeat(80));
    console.log('🎉 Integration Tests Complete!');
    console.log('\n✅ Summary:');
    console.log('  - Auth Service: User profiles and schools management');
    console.log('  - Archive Service: Demographic analytics and insights');
    console.log('  - Cross-service data access working correctly');
    console.log('\n📚 Next Steps:');
    console.log('  1. Review the NEW_TABLES_INTEGRATION.md documentation');
    console.log('  2. Test the new API endpoints');
    console.log('  3. Import initial school data if needed');
    console.log('  4. Monitor logs for any issues');
    
  } catch (error) {
    console.error('\n❌ Integration test failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Run if this script is executed directly
if (require.main === module) {
  runIntegrationTests();
}

module.exports = { runIntegrationTests };
