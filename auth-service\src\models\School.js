const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const School = sequelize.define('School', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    allowNull: false
  },
  name: {
    type: DataTypes.STRING(200),
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [1, 200]
    }
  },
  address: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  city: {
    type: DataTypes.STRING(100),
    allowNull: true,
    validate: {
      len: [1, 100]
    }
  },
  province: {
    type: DataTypes.STRING(100),
    allowNull: true,
    validate: {
      len: [1, 100]
    }
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    field: 'created_at'
  }
}, {
  tableName: 'schools',
  schema: 'public', // Schools table is in public schema
  timestamps: false, // Only has created_at, no updated_at
  underscored: true,
  indexes: [
    {
      fields: ['name']
    },
    {
      fields: ['city']
    },
    {
      fields: ['province']
    },
    {
      fields: ['created_at']
    }
  ]
});

// Instance methods
School.prototype.toJSON = function() {
  const values = Object.assign({}, this.get());
  return values;
};

// Class methods
School.associate = function(models) {
  // Define associations here if needed
  // For example, if we want to relate schools to user profiles
};

module.exports = School;
