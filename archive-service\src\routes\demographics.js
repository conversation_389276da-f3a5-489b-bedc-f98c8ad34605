/**
 * Demographics Routes
 * Defines routes for demographic analytics and insights
 */

const express = require('express');
const demographicController = require('../controllers/demographicController');
const { authenticateService, requireServiceAuth } = require('../middleware/serviceAuth');

const router = express.Router();

// Apply service authentication middleware to all routes
router.use(authenticateService);

/**
 * GET /archive/demographics/overview
 * Get overall demographic overview (internal service only)
 */
router.get('/overview',
  requireServiceAuth,
  demographicController.getDemographicOverview
);

/**
 * GET /archive/demographics/archetype/:archetype
 * Get demographic insights for a specific archetype (internal service only)
 */
router.get('/archetype/:archetype',
  requireServiceAuth,
  demographicController.getArchetypeDemographics
);

/**
 * GET /archive/demographics/schools
 * Get school-based analytics (internal service only)
 * Query parameters:
 * - school: Optional school name filter
 */
router.get('/schools',
  requireService<PERSON><PERSON>,
  demographicController.getSchoolAnalytics
);

module.exports = router;
