/**
 * Models Index for Archive Service
 * Exports all models and sets up associations
 */

const { sequelize } = require('../config/database');
const AnalysisResult = require('./AnalysisResult');
const UserProfile = require('./UserProfile');
const School = require('./School');

// Initialize models
const models = {
  AnalysisResult,
  UserProfile,
  School,
  sequelize
};

// Set up associations
Object.keys(models).forEach(modelName => {
  if (models[modelName].associate) {
    models[modelName].associate(models);
  }
});

module.exports = models;
